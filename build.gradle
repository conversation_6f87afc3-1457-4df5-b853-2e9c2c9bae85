// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.8.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.10'
        classpath 'com.google.gms:google-services:4.4.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.48'
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:2.7.7"
    }
}

plugins {
    id 'com.android.application' version '8.8.2' apply false
    id 'com.android.library' version '8.8.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.22' apply false
    id 'com.google.gms.google-services' version '4.4.2' apply false
    id 'com.diffplug.spotless' version '6.23.3' apply false
}

// Nothing else here - we'll apply Spotless directly in app/build.gradle