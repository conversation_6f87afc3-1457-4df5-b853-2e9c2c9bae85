package com.example.allinone.api

import android.util.Log
import com.example.allinone.data.BinanceBalance
import com.example.allinone.BuildConfig
import com.example.allinone.data.BinanceFutures
import com.example.allinone.data.BinanceOrder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.util.Date
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import java.net.URL
import java.net.HttpURLConnection
import java.security.InvalidKeyException
import java.security.NoSuchAlgorithmException
import java.io.OutputStreamWriter

/**
 * Client for interacting with the Binance Futures API
 */
class BinanceApiClient(
    private val apiKey: String = BuildConfig.BINANCE_API_KEY,
    private val secretKey: String = BuildConfig.BINANCE_SECRET_KEY,
    private val baseUrl: String = "https://fapi.binance.com"
) {
    private val TAG = "BinanceApiClient"

    /**
     * Get account balance from Binance Futures API
     */
    suspend fun getAccountBalance(): List<BinanceBalance> = withContext(Dispatchers.IO) {
        try {
            val timestamp = System.currentTimeMillis().toString()
            val queryString = "timestamp=$timestamp"
            val signature = generateSignature(queryString)

            val url = URL("$baseUrl/fapi/v2/balance?$queryString&signature=$signature")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.setRequestProperty("X-MBX-APIKEY", apiKey)

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                parseBalanceResponse(response)
            } else {
                val errorResponse = connection.errorStream.bufferedReader().use { it.readText() }
                Log.e(TAG, "Error fetching account balance: $errorResponse")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception fetching account balance", e)
            emptyList()
        }
    }

    /**
     * Get latest prices for all USD-M Futures trading pairs
     * Returns a map of symbol to price
     */
    suspend fun getLatestPrices(): Map<String, Double> = withContext(Dispatchers.IO) {
        val priceMap = mutableMapOf<String, Double>()
        try {
            // This endpoint doesn't require authentication
            val url = URL("$baseUrl/fapi/v1/ticker/price")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                Log.d(TAG, "USD-M Prices Response: $response")

                val jsonArray = JSONArray(response)
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    val symbol = jsonObject.getString("symbol")
                    val price = jsonObject.getString("price").toDouble()
                    priceMap[symbol] = price

                    // Also add a simplified version of the symbol (without USDT suffix)
                    // For example, for BTCUSDT, also add BTC=price
                    val baseAsset = symbol.replace("USDT", "")
                    if (baseAsset.isNotEmpty()) {
                        priceMap[baseAsset] = price
                    }
                }
                Log.d(TAG, "USD-M Prices parsed: ${priceMap.size} prices")
            } else {
                val errorResponse = connection.errorStream.bufferedReader().use { it.readText() }
                Log.e(TAG, "Error fetching prices: $errorResponse")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception fetching prices", e)
        }
        return@withContext priceMap
    }

    /**
     * Get position information from Binance Futures API
     */
    suspend fun getPositionInformation(): List<BinanceFutures> = withContext(Dispatchers.IO) {
        try {
            val timestamp = System.currentTimeMillis().toString()
            val queryString = "timestamp=$timestamp"
            val signature = generateSignature(queryString)

            val url = URL("$baseUrl/fapi/v2/positionRisk?$queryString&signature=$signature")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.setRequestProperty("X-MBX-APIKEY", apiKey)

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                parsePositionResponse(response)
            } else {
                val errorResponse = connection.errorStream.bufferedReader().use { it.readText() }
                Log.e(TAG, "Error fetching position information: $errorResponse")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception fetching position information", e)
            emptyList()
        }
    }

    /**
     * Get all open orders from Binance Futures API
     * @return List of open orders
     */
    suspend fun getOpenOrders(): List<BinanceOrder> = withContext(Dispatchers.IO) {
        try {
            val timestamp = System.currentTimeMillis().toString()
            val queryString = "timestamp=$timestamp"
            val signature = generateSignature(queryString)

            val url = URL("$baseUrl/fapi/v1/openOrders?$queryString&signature=$signature")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.setRequestProperty("X-MBX-APIKEY", apiKey)

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                Log.d(TAG, "Open Orders Response: $response")
                parseOpenOrdersResponse(response)
            } else {
                val errorResponse = connection.errorStream.bufferedReader().use { it.readText() }
                Log.e(TAG, "Error fetching open orders: $errorResponse")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception fetching open orders", e)
            emptyList()
        }
    }

    /**
     * Parse the balance response from Binance API
     */
    private fun parseBalanceResponse(response: String): List<BinanceBalance> {
        val balances = mutableListOf<BinanceBalance>()
        try {
            val jsonArray = JSONArray(response)
            for (i in 0 until jsonArray.length()) {
                val jsonObject = jsonArray.getJSONObject(i)
                val asset = jsonObject.getString("asset")
                val balance = jsonObject.getString("balance").toDouble()
                val crossWalletBalance = jsonObject.getString("crossWalletBalance").toDouble()
                val crossUnPnl = jsonObject.getString("crossUnPnl").toDouble()
                val availableBalance = jsonObject.getString("availableBalance").toDouble()
                val maxWithdrawAmount = jsonObject.getString("maxWithdrawAmount").toDouble()
                val marginAvailable = jsonObject.getBoolean("marginAvailable")
                val updateTime = Date(jsonObject.getLong("updateTime"))

                balances.add(
                    BinanceBalance(
                        id = i.toLong(),
                        asset = asset,
                        balance = balance,
                        crossWalletBalance = crossWalletBalance,
                        crossUnPnl = crossUnPnl,
                        availableBalance = availableBalance,
                        maxWithdrawAmount = maxWithdrawAmount,
                        marginAvailable = marginAvailable,
                        updateTime = updateTime,
                        futuresType = "USD-M"
                    )
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing balance response", e)
        }
        return balances
    }

    /**
     * Parse the position response from Binance API
     */
    private fun parsePositionResponse(response: String): List<BinanceFutures> {
        val positions = mutableListOf<BinanceFutures>()
        try {
            val jsonArray = JSONArray(response)
            for (i in 0 until jsonArray.length()) {
                val jsonObject = jsonArray.getJSONObject(i)
                val symbol = jsonObject.getString("symbol")
                val positionAmt = jsonObject.getString("positionAmt").toDouble()

                // Skip positions with zero amount
                if (positionAmt == 0.0) continue

                val entryPrice = jsonObject.getString("entryPrice").toDouble()
                val markPrice = jsonObject.getString("markPrice").toDouble()
                val unRealizedProfit = jsonObject.getString("unRealizedProfit").toDouble()
                val liquidationPrice = jsonObject.getString("liquidationPrice").toDouble()
                val leverage = jsonObject.getInt("leverage")
                val marginType = jsonObject.getString("marginType")
                val isolatedMargin = if (jsonObject.has("isolatedMargin"))
                    jsonObject.getString("isolatedMargin").toDouble() else 0.0
                val isAutoAddMargin = jsonObject.getBoolean("isAutoAddMargin")
                val positionSide = jsonObject.getString("positionSide")
                val updateTime = Date(jsonObject.getLong("updateTime"))

                positions.add(
                    BinanceFutures(
                        id = i.toLong(),
                        symbol = symbol,
                        positionAmt = positionAmt,
                        entryPrice = entryPrice,
                        markPrice = markPrice,
                        unRealizedProfit = unRealizedProfit,
                        liquidationPrice = liquidationPrice,
                        leverage = leverage,
                        marginType = marginType,
                        isolatedMargin = isolatedMargin,
                        isAutoAddMargin = isAutoAddMargin,
                        positionSide = positionSide,
                        updateTime = updateTime,
                        futuresType = "USD-M"
                    )
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing position response", e)
        }
        return positions
    }

    /**
     * Generate HMAC SHA256 signature for API request
     */
    private fun generateSignature(data: String): String {
        try {
            val hmacSha256 = "HmacSHA256"
            val secretKeySpec = SecretKeySpec(secretKey.toByteArray(), hmacSha256)
            val mac = Mac.getInstance(hmacSha256)
            mac.init(secretKeySpec)
            val hash = mac.doFinal(data.toByteArray())
            return bytesToHex(hash)
        } catch (e: NoSuchAlgorithmException) {
            Log.e(TAG, "No such algorithm", e)
        } catch (e: InvalidKeyException) {
            Log.e(TAG, "Invalid key", e)
        }
        return ""
    }

    /**
     * Convert bytes to hexadecimal string
     */
    private fun bytesToHex(bytes: ByteArray): String {
        val hexChars = "0123456789abcdef"
        val result = StringBuilder(bytes.size * 2)
        for (byte in bytes) {
            val i = byte.toInt() and 0xff
            result.append(hexChars[i shr 4])
            result.append(hexChars[i and 0x0f])
        }
        return result.toString()
    }

    /**
     * Place a Take Profit Market order
     * @param symbol The trading pair symbol (e.g., BTCUSDT)
     * @param side BUY or SELL
     * @param quantity The quantity to trade
     * @param stopPrice The price at which the order will be triggered
     * @return The response from the API as a JSON string
     */
    suspend fun placeTakeProfitMarketOrder(
        symbol: String,
        side: String,
        quantity: Double,
        stopPrice: Double
    ): String = withContext(Dispatchers.IO) {
        try {
            val timestamp = System.currentTimeMillis().toString()
            val queryString = "symbol=$symbol&side=$side&type=TAKE_PROFIT_MARKET&quantity=$quantity&stopPrice=$stopPrice&closePosition=true&workingType=CONTRACT_PRICE&timeInForce=GTE_GTC&timestamp=$timestamp"
            val signature = generateSignature(queryString)

            val url = URL("$baseUrl/fapi/v1/order?$queryString&signature=$signature")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.setRequestProperty("X-MBX-APIKEY", apiKey)
            connection.doOutput = true

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                Log.d(TAG, "Take Profit Market Order Response: $response")
                response
            } else {
                val errorResponse = connection.errorStream.bufferedReader().use { it.readText() }
                Log.e(TAG, "Error placing Take Profit Market order: $errorResponse")
                "{\"error\":\"$errorResponse\"}"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception placing Take Profit Market order", e)
            "{\"error\":\"${e.message}\"}"
        }
    }

    /**
     * Place a Stop Loss Market order
     * @param symbol The trading pair symbol (e.g., BTCUSDT)
     * @param side BUY or SELL
     * @param quantity The quantity to trade
     * @param stopPrice The price at which the order will be triggered
     * @return The response from the API as a JSON string
     */
    suspend fun placeStopLossMarketOrder(
        symbol: String,
        side: String,
        quantity: Double,
        stopPrice: Double
    ): String = withContext(Dispatchers.IO) {
        try {
            val timestamp = System.currentTimeMillis().toString()
            val queryString = "symbol=$symbol&side=$side&type=STOP_MARKET&quantity=$quantity&stopPrice=$stopPrice&closePosition=true&workingType=MARK_PRICE&timeInForce=GTE_GTC&timestamp=$timestamp"
            val signature = generateSignature(queryString)

            val url = URL("$baseUrl/fapi/v1/order?$queryString&signature=$signature")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.setRequestProperty("X-MBX-APIKEY", apiKey)
            connection.doOutput = true

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                Log.d(TAG, "Stop Loss Market Order Response: $response")
                response
            } else {
                val errorResponse = connection.errorStream.bufferedReader().use { it.readText() }
                Log.e(TAG, "Error placing Stop Loss Market order: $errorResponse")
                "{\"error\":\"$errorResponse\"}"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception placing Stop Loss Market order", e)
            "{\"error\":\"${e.message}\"}"
        }
    }

    /**
     * Parse the open orders response from Binance API
     */
    private fun parseOpenOrdersResponse(response: String): List<BinanceOrder> {
        val orders = mutableListOf<BinanceOrder>()
        try {
            val jsonArray = JSONArray(response)
            for (i in 0 until jsonArray.length()) {
                val jsonObject = jsonArray.getJSONObject(i)
                val symbol = jsonObject.getString("symbol")
                val orderId = jsonObject.getLong("orderId")
                val type = jsonObject.getString("type")
                val side = jsonObject.getString("side")
                val price = jsonObject.optDouble("price", 0.0)
                val stopPrice = jsonObject.optDouble("stopPrice", 0.0)
                val origQty = jsonObject.optDouble("origQty", 0.0)
                val positionSide = jsonObject.optString("positionSide", "BOTH")

                // Only include TP/SL orders
                if (type == "TAKE_PROFIT_MARKET" || type == "STOP_MARKET") {
                    orders.add(
                        BinanceOrder(
                            symbol = symbol,
                            orderId = orderId,
                            type = type,
                            side = side,
                            price = price,
                            stopPrice = stopPrice,
                            origQty = origQty,
                            positionSide = positionSide
                        )
                    )
                }
            }
            Log.d(TAG, "Parsed ${orders.size} TP/SL orders")
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing open orders response", e)
        }
        return orders
    }

    /**
     * Close a position by placing a market order in the opposite direction
     * @param symbol The trading pair symbol (e.g., BTCUSDT)
     * @param positionAmt The current position amount (positive for long, negative for short)
     * @return The response from the API as a JSON string
     */
    suspend fun closePosition(
        symbol: String,
        positionAmt: Double
    ): String = withContext(Dispatchers.IO) {
        try {
            // Determine the side based on the position amount
            // If positionAmt is positive (long), we need to SELL to close
            // If positionAmt is negative (short), we need to BUY to close
            val side = if (positionAmt > 0) "SELL" else "BUY"
            // Use absolute value of position amount for the quantity
            val quantity = Math.abs(positionAmt)

            val timestamp = System.currentTimeMillis().toString()
            val queryString = "symbol=$symbol&side=$side&type=MARKET&quantity=$quantity&timestamp=$timestamp"
            val signature = generateSignature(queryString)

            val url = URL("$baseUrl/fapi/v1/order?$queryString&signature=$signature")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.setRequestProperty("X-MBX-APIKEY", apiKey)
            connection.doOutput = true

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                Log.d(TAG, "Close Position Response: $response")
                response
            } else {
                val errorResponse = connection.errorStream.bufferedReader().use { it.readText() }
                Log.e(TAG, "Error closing position: $errorResponse")
                "{\"error\":\"$errorResponse\"}"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception closing position", e)
            "{\"error\":\"${e.message}\"}"
        }
    }

    /**
     * Cancel an order by its ID
     * @param symbol The trading pair symbol (e.g., BTCUSDT)
     * @param orderId The ID of the order to cancel
     * @return The response from the API as a JSON string
     */
    suspend fun cancelOrder(
        symbol: String,
        orderId: Long
    ): String = withContext(Dispatchers.IO) {
        try {
            val timestamp = System.currentTimeMillis().toString()
            val queryString = "symbol=$symbol&orderId=$orderId&timestamp=$timestamp"
            val signature = generateSignature(queryString)

            val url = URL("$baseUrl/fapi/v1/order?$queryString&signature=$signature")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "DELETE"
            connection.setRequestProperty("X-MBX-APIKEY", apiKey)

            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                Log.d(TAG, "Cancel Order Response: $response")
                response
            } else {
                val errorResponse = connection.errorStream.bufferedReader().use { it.readText() }
                Log.e(TAG, "Error canceling order: $errorResponse")
                "{\"error\":\"$errorResponse\"}"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception canceling order", e)
            "{\"error\":\"${e.message}\"}"
        }
    }
}
