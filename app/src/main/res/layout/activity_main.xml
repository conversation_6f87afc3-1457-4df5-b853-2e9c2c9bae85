<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/windowBackground"
    tools:openDrawer="start">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:attr/windowBackground">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                android:elevation="4dp"
                android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar"
                app:popupTheme="@style/ThemeOverlay.MaterialComponents.Light" />

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/nav_host_fragment"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="?android:attr/windowBackground"
            app:defaultNavHost="true"
            app:layout_constraintBottom_toTopOf="@id/bottomNavigation"
            app:layout_constraintTop_toBottomOf="@id/appBarLayout"
            app:navGraph="@navigation/mobile_navigation" />

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottomNavigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?android:attr/windowBackground"
            android:visibility="visible"
            android:elevation="8dp"
            app:itemIconTint="@color/bottom_nav_item_color_light"
            app:itemTextColor="@color/bottom_nav_item_color_light"
            app:layout_constraintBottom_toBottomOf="parent"
            app:menu="@menu/bottom_nav_menu" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Properly structured navigation drawer with theme switch -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        style="@style/Widget.App.NavigationView"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/drawer_menu">

        <!-- Theme switch at the bottom of the drawer -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="?android:attr/listDivider" />

            <include
                android:id="@+id/themeToggleLayout"
                layout="@layout/theme_switch_layout" />
        </LinearLayout>
    </com.google.android.material.navigation.NavigationView>

</androidx.drawerlayout.widget.DrawerLayout> 