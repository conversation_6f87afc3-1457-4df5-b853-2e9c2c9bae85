<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@drawable/border_background"
    android:layout_marginBottom="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Exercise Name"
            app:boxBackgroundMode="outline">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/exercise_name_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:background="@android:color/white" />
        </com.google.android.material.textfield.TextInputLayout>

        <ImageButton
            android:id="@+id/remove_exercise_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Remove Exercise"
            android:padding="8dp"
            android:src="@drawable/ic_remove" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:hint="Sets"
            app:boxBackgroundMode="outline">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/exercise_sets_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:background="@android:color/white" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:hint="Reps"
            app:boxBackgroundMode="outline">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/exercise_reps_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:background="@android:color/white" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_weight="1"
            android:hint="Weight (kg)"
            app:boxBackgroundMode="outline">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/exercise_weight_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal"
                android:background="@android:color/white" />
        </com.google.android.material.textfield.TextInputLayout>
    </LinearLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
        android:hint="Muscle Group">

        <AutoCompleteTextView
            android:id="@+id/muscle_group_dropdown"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:background="@android:color/white" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="Notes (Optional)"
        app:boxBackgroundMode="outline">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/exercise_notes_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:minLines="1"
            android:background="@android:color/white" />
    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>
