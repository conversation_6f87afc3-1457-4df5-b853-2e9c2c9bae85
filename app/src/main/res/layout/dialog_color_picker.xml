<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <org.maroxa.diario.custom.ColorPickerView
        android:id="@+id/color_picker_view"
        android:layout_width="240dp"
        android:layout_height="240dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/brightness"
        android:layout_marginBottom="8dp" />

    <SeekBar
        android:id="@+id/brightness_slider"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:max="100"
        android:progress="100" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@android:string/cancel"
            android:layout_marginEnd="8dp"
            android:textColor="@android:color/black"
            style="?android:attr/borderlessButtonStyle" />

        <Button
            android:id="@+id/btn_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@android:string/ok"
            android:textColor="@android:color/black"
            style="?android:attr/borderlessButtonStyle" />
    </LinearLayout>

</LinearLayout> 