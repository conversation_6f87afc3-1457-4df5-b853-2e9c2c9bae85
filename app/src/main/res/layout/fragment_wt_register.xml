<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Network status banner -->
    <TextView
        android:id="@+id/networkStatusBanner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorWarning"
        android:padding="8dp"
        android:text="@string/network_unavailable"
        android:textAlignment="center"
        android:textColor="@android:color/white"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Month filter section -->
    <LinearLayout
        android:id="@+id/filterSection"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="14dp"
        android:paddingEnd="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/networkStatusBanner">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="@string/select_month"
                android:layout_gravity="center_vertical"
                app:boxStrokeWidth="1dp"
                app:hintTextAppearance="@style/TextAppearance.AppCompat.Small">

                <AutoCompleteTextView
                    android:id="@+id/monthDropdown"
                    android:layout_width="match_parent"
                    android:inputType="none"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    style="@style/FilterDropdownStyle" />
            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/applyFilterButton"
                android:layout_width="wrap_content"
                android:text="@string/apply"
                android:layout_gravity="center_vertical"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                style="@style/FilterButtonStyle" />
        </LinearLayout>

        <TextView
            android:id="@+id/totalAmountText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="16sp"
            android:textAlignment="textEnd"
            android:visibility="gone" />
    </LinearLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/filterSection">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/studentsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="16dp" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <LinearLayout
        android:id="@+id/emptyState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:contentDescription="@string/no_registrations"
            android:src="@drawable/ic_no_registrations" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/no_registrations"
            android:textAppearance="?attr/textAppearanceHeadline6" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="@string/no_registrations_prompt"
            android:textAppearance="?attr/textAppearanceBody2" />

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addStudentFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:contentDescription="@string/add_registration"
        android:src="@drawable/ic_add"
        app:backgroundTint="@color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:tint="@color/white" />

</androidx.constraintlayout.widget.ConstraintLayout>