<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">
        
        <ImageView
            android:id="@+id/postDetailThumbnail"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:scaleType="centerCrop"
            android:background="#E0E0E0"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/postCaptionLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Caption:"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postDetailThumbnail"
            android:layout_marginTop="16dp" />

        <TextView
            android:id="@+id/postCaption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:autoLink="none"
            android:linksClickable="false"
            android:textColorLink="@android:color/holo_blue_dark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/postCaptionLabel"
            app:layout_constraintTop_toTopOf="@+id/postCaptionLabel"
            tools:text="This is an example caption" />

        <TextView
            android:id="@+id/postDateLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Posted:"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postCaption" />

        <TextView
            android:id="@+id/postDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Posted on: Apr 08, 2023"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postDateLabel" />

        <TextView
            android:id="@+id/postTypeLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Media Type:"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postDate" />

        <TextView
            android:id="@+id/postType"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Media Type: REELS"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postTypeLabel" />

        <TextView
            android:id="@+id/postLinkLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Link:"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postType" />

        <TextView
            android:id="@+id/postLink"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Link: https://instagram.com/p/example"
            android:textColor="#1976D2"
            android:textSize="14sp"
            android:autoLink="none"
            android:textIsSelectable="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postLinkLabel" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="@android:color/darker_gray"
            app:layout_constraintTop_toBottomOf="@+id/postLink" />

        <TextView
            android:id="@+id/insightsLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="INSIGHTS:"
            android:textStyle="bold"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/divider" />

        <TextView
            android:id="@+id/postInsights"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:autoLink="none"
            android:linksClickable="false"
            android:textColorLink="@android:color/holo_blue_dark"
            android:lineSpacingExtra="4dp"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/insightsLabel"
            tools:text="👁️ Reach: 461\n❤️ Likes: 22\n💬 Comments: 0\n📺 Views: 731\n🔖 Saved: 0\n🔄 Shares: 3" />

        <Button
            android:id="@+id/closeButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Close"
            android:textAllCaps="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/postInsights" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView> 