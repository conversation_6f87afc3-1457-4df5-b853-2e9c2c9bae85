<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/windowBackground">

    <FrameLayout
        android:id="@+id/wtFragmentContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="?android:attr/windowBackground"
        app:layout_constraintBottom_toTopOf="@id/wtBottomNavigation"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/wtBottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:attr/windowBackground"
        app:labelVisibilityMode="labeled"
        app:itemIconTint="@color/bottom_nav_item_color_light"
        app:itemTextColor="@color/bottom_nav_item_color_light"
        app:layout_constraintBottom_toBottomOf="parent"
        app:menu="@menu/wt_bottom_nav_menu" />

</androidx.constraintlayout.widget.ConstraintLayout> 