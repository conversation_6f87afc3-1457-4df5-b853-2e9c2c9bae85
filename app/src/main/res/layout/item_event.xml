<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="16dp">

            <TextView
                android:id="@+id/eventTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                style="@style/BoldText"
                android:text="18:00" />

            <TextView
                android:id="@+id/eventDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:text="22 Feb" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_gravity="center_vertical"
            android:gravity="start">

            <TextView
                android:id="@+id/eventTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                style="@style/BoldText"
                android:text="Event Title" />

            <TextView
                android:id="@+id/eventTypeTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_tag_green"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:text="Event" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 