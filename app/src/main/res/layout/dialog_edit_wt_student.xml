<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/select_student">

            <AutoCompleteTextView
                android:id="@+id/studentDropdown"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="@string/start_date">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/startDateInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="@string/end_date">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/endDateInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/amountLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="Amount"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/amountInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:hint="@string/notes">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/notesEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:minLines="2" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/paidSwitch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/payment_received" />

        <!-- Receipt section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/payment_receipt"
            android:textAppearance="?attr/textAppearanceSubtitle1" />
        
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical">
            
            <TextView
                android:id="@+id/attachmentNameText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/receipt_unavailable"
                android:ellipsize="middle"
                android:singleLine="true" />
                
            <Button
                android:id="@+id/addAttachmentButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/add_receipt"
                android:layout_marginStart="8dp" />
        </LinearLayout>
        
        <ImageView
            android:id="@+id/attachmentPreview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:visibility="gone" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView> 