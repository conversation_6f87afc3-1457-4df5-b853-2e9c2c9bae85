<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/workout_name_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="?attr/textAppearanceHeadline5"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Upper Body Workout" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/timer_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/workout_name_title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Workout Timer"
                android:textAppearance="?attr/textAppearanceHeadline6" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:orientation="horizontal">
                
                <TextView
                    android:id="@+id/timer_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="00:00:00"
                    android:textAppearance="?attr/textAppearanceHeadline3"
                    android:textStyle="bold"
                    android:textColor="#4FC3F7" />
                    
                <TextView
                    android:id="@+id/timer_ms_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="12dp"
                    android:text=".00"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textStyle="bold"
                    android:textColor="#4FC3F7" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/play_pause_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="Pause"
                    app:icon="@drawable/ic_pause" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/stop_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Stop"
                    app:icon="@drawable/ic_stop" />

            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/exercises_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Exercises"
        android:textAppearance="?attr/textAppearanceHeadline6"
        app:layout_constraintTop_toBottomOf="@id/timer_card" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/exercises_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/exercises_title"
        tools:listitem="@layout/item_workout_exercise" />

</androidx.constraintlayout.widget.ConstraintLayout> 