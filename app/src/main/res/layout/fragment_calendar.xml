<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/windowBackground"
    tools:context=".ui.CalendarFragment">

    <!-- Calendar Header -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/calendarHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="8dp"
        android:background="?android:attr/windowBackground"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/prevMonthButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Previous Month"
            android:src="@drawable/ic_chevron_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/monthYearText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="March 2023"
            android:textSize="20sp"
            style="@style/BoldText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/nextMonthButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Next Month"
            android:src="@drawable/ic_chevron_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Day of Week Labels -->
    <LinearLayout
        android:id="@+id/weekDaysHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingVertical="8dp"
        app:layout_constraintTop_toBottomOf="@id/calendarHeader">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="MON"
            style="@style/BoldText" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="TUE"
            style="@style/BoldText" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="WED"
            style="@style/BoldText" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="THU"
            style="@style/BoldText" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="FRI"
            style="@style/BoldText" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="SAT"
            style="@style/BoldText" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="SUN"
            style="@style/BoldText" />

    </LinearLayout>

    <!-- Calendar Grid - Week 1 -->
    <LinearLayout
        android:id="@+id/week1"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintDimensionRatio="7:1"
        app:layout_constraintTop_toBottomOf="@id/weekDaysHeader">

        <TextView
            android:id="@+id/day1"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day2"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day3"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day4"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day5"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day6"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day7"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- Calendar Grid - Week 2 -->
    <LinearLayout
        android:id="@+id/week2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintDimensionRatio="7:1"
        app:layout_constraintTop_toBottomOf="@id/week1">

        <TextView
            android:id="@+id/day8"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day9"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day10"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day11"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day12"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day13"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day14"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- Calendar Grid - Week 3 -->
    <LinearLayout
        android:id="@+id/week3"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintDimensionRatio="7:1"
        app:layout_constraintTop_toBottomOf="@id/week2">

        <TextView
            android:id="@+id/day15"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day16"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day17"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day18"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day19"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day20"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day21"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- Calendar Grid - Week 4 -->
    <LinearLayout
        android:id="@+id/week4"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintDimensionRatio="7:1"
        app:layout_constraintTop_toBottomOf="@id/week3">

        <TextView
            android:id="@+id/day22"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day23"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day24"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day25"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day26"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day27"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day28"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- Calendar Grid - Week 5 -->
    <LinearLayout
        android:id="@+id/week5"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintDimensionRatio="7:1"
        app:layout_constraintTop_toBottomOf="@id/week4">

        <TextView
            android:id="@+id/day29"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day30"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day31"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day32"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day33"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day34"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day35"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- Calendar Grid - Week 6 -->
    <LinearLayout
        android:id="@+id/week6"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintDimensionRatio="7:1"
        app:layout_constraintTop_toBottomOf="@id/week5">

        <TextView
            android:id="@+id/day36"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day37"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day38"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day39"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day40"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day41"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/day42"
            style="@style/CalendarDayStyle"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- Information text -->
    <TextView
        android:id="@+id/infoText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="Long press on a date to add an event"
        android:textStyle="italic"
        app:layout_constraintTop_toBottomOf="@id/week6" />

    <!-- Events Section Header -->
    <TextView
        android:id="@+id/eventsHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginHorizontal="16dp"
        android:text="Upcoming Events"
        android:textSize="18sp"
        style="@style/BoldText"
        app:layout_constraintTop_toBottomOf="@id/infoText" />

    <!-- Events List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/eventsRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginHorizontal="16dp"
        app:layout_constraintTop_toBottomOf="@id/eventsHeader"
        app:layout_constraintBottom_toBottomOf="parent"
        android:clipToPadding="false"
        android:paddingBottom="16dp" />

    <!-- Empty state view for when there are no events -->
    <TextView
        android:id="@+id/emptyEventsText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="No events for the selected month"
        android:textStyle="italic"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/eventsHeader" />

</androidx.constraintlayout.widget.ConstraintLayout> 