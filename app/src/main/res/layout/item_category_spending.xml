<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">

    <View
        android:id="@+id/categoryColorIndicator"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_gravity="center_vertical"
        android:background="@drawable/circle_shape"
        android:layout_marginEnd="8dp"/>

    <TextView
        android:id="@+id/categoryNameText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="14sp"
        android:textColor="@color/black"
        tools:text="Food"/>

    <TextView
        android:id="@+id/categoryAmountText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginEnd="8dp"
        tools:text="₺850.00"/>

    <TextView
        android:id="@+id/categoryPercentText"
        android:layout_width="48dp"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/gray_dark"
        android:gravity="end"
        tools:text="35%"/>
</LinearLayout>
