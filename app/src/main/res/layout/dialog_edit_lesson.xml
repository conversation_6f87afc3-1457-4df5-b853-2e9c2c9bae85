<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Day:"
        style="@style/BoldText" />

    <TextView
        android:id="@+id/dayText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Monday"
        android:textSize="16sp"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Time:"
        style="@style/BoldText"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">
        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Start: "
            android:layout_gravity="center_vertical" />

        <EditText
            android:id="@+id/startTimeField"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:hint="Type 4 digits (HHMM)"
            android:maxLength="5"
            android:textAlignment="center" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="End: "
            android:layout_gravity="center_vertical" />

        <EditText
            android:id="@+id/endTimeField"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:hint="Type 4 digits (HHMM)"
            android:maxLength="5"
            android:textAlignment="center" />
    </LinearLayout>

</LinearLayout> 