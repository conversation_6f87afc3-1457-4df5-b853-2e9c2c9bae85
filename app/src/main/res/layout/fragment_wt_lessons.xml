<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/lessonDaysLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Select Days"
                android:textSize="16sp"
                style="@style/BoldText"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/daysChipGroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:chipSpacingHorizontal="4dp"
                app:singleSelection="false"
                android:layout_marginBottom="16dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/mondayChip"
                    style="@style/Widget.MaterialComponents.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mon" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/tuesdayChip"
                    style="@style/Widget.MaterialComponents.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tue" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/wednesdayChip"
                    style="@style/Widget.MaterialComponents.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Wed" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/thursdayChip"
                    style="@style/Widget.MaterialComponents.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thu" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/fridayChip"
                    style="@style/Widget.MaterialComponents.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fri" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/saturdayChip"
                    style="@style/Widget.MaterialComponents.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Sat" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/sundayChip"
                    style="@style/Widget.MaterialComponents.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Sun" />
            </com.google.android.material.chip.ChipGroup>

            <TextView
                android:id="@+id/lessonTimeLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Lesson Time"
                android:textSize="16sp"
                style="@style/BoldText"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Start: "
                    android:layout_gravity="center_vertical" />

                <EditText
                    android:id="@+id/startTimeField"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:hint="Type 4 digits (HHMM)"
                    android:maxLength="5"
                    android:textAlignment="center" />

                <Space
                    android:layout_width="16dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="End: "
                    android:layout_gravity="center_vertical" />

                <EditText
                    android:id="@+id/endTimeField"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:hint="Type 4 digits (HHMM)"
                    android:maxLength="5"
                    android:textAlignment="center" />
            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/addLessonButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Add Lesson(s)"
                android:layout_marginBottom="16dp" />

            <TextView
                android:id="@+id/lessonsLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Current Lessons"
                android:textSize="16sp"
                style="@style/BoldText"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:id="@+id/lessonsContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" />

            <TextView
                android:id="@+id/noLessonsText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="No lessons scheduled yet"
                android:gravity="center"
                android:textStyle="italic"
                android:visibility="gone" />
        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 