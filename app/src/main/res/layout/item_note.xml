<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/noteTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/shareButton"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Note Title" />

        <ImageButton
            android:id="@+id/shareButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Share Note"
            android:padding="8dp"
            android:src="@drawable/ic_share"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/noteDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noteTitle"
            tools:text="January 1, 2023" />

        <TextView
            android:id="@+id/noteContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textAppearance="?attr/textAppearanceBody2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noteDate"
            tools:text="This is the content of the note that might be very long and span multiple lines. It will be truncated after 3 lines." />

        <!-- Attachments Section -->
        <LinearLayout
            android:id="@+id/attachmentsSection"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noteContent">

            <!-- Voice Notes Indicator -->
            <LinearLayout
                android:id="@+id/voiceNoteIndicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp"
                android:gravity="center_vertical"
                android:visibility="gone"
                android:padding="4dp"
                android:background="@drawable/rounded_background"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_btn_speak_now"
                    android:contentDescription="@string/voice_notes" />

                <TextView
                    android:id="@+id/voiceNoteCountText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurface"
                    tools:text="2 voice notes" />

            </LinearLayout>

            <!-- Image Previews -->
            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/imageContainer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </HorizontalScrollView>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView> 