<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#80000000" />
            <padding
                android:bottom="4dp"
                android:left="4dp"
                android:right="4dp"
                android:top="4dp" />
        </shape>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#2196F3" />
            <padding
                android:bottom="4dp"
                android:left="4dp"
                android:right="4dp"
                android:top="4dp" />
        </shape>
    </item>
</selector> 