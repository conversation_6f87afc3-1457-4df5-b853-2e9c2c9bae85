<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Add Material TextInput default box stroke color -->
    <color name="mtrl_textinput_default_box_stroke_color">@color/gray</color>

    <!-- Base application theme. -->
    <style name="Theme.AllinOne" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryVariant">@color/gray_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/black</item>
        <item name="colorSecondaryVariant">@color/gray_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/black</item>
        <!-- Explicitly disable ActionBar -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <!-- Ensure background is not black by default -->
        <item name="android:windowBackground">@color/white</item>
        <!-- Navigation colors -->
        <item name="colorNavigationItem">@color/black</item>
        <item name="colorNavigationItemSelected">@color/gray_dark</item>
    </style>
    
    <!-- Theme without action bar and full screen for drawing activity -->
    <style name="Theme.AllinOne.NoActionBar" parent="Theme.AllinOne">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/black</item>
        <item name="android:windowBackground">@android:color/white</item>
    </style>

    <!-- Theme for app startup with a real proper text -->
    <style name="Theme.AllinOne.SplashText" parent="Theme.AllinOne">
        <item name="android:windowBackground">@color/black</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">shortEdges</item>
    </style>

    <!-- Splash theme with minimal impact - just blocks system theme -->
    <style name="Theme.AllinOne.Starting" parent="Theme.SplashScreen">
        <!-- Set the splash screen background to a solid color matching your app -->
        <item name="windowSplashScreenBackground">@color/black</item>
        
        <!-- Use a tiny transparent pixel - the real text will be in our layout -->
        <item name="windowSplashScreenAnimatedIcon">@drawable/transparent</item>
        
        <!-- Set the theme of the Activity that directly follows the splash screen -->
        <item name="postSplashScreenTheme">@style/Theme.AllinOne</item>
        
        <!-- Disable any preview windows -->
        <item name="android:windowDisablePreview">true</item>
    </style>
</resources>