<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.8.1" type="baseline" client="gradle" dependencies="false" name="AGP (8.8.1)" variant="all" version="8.8.1">

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            amountText.text = String.format(&quot;₺%.2f&quot;, item.amount)"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/CategorySummaryAdapter.kt"
            line="33"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            amountText.text = String.format(&quot;₺%.2f&quot;, item.amount)"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/CategorySummaryAdapter.kt"
            line="33"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.balanceText.text = String.format(&quot;₺%.2f&quot;, balance)"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/HomeFragment.kt"
            line="65"
            column="40"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.balanceText.text = String.format(&quot;₺%.2f&quot;, balance)"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/HomeFragment.kt"
            line="65"
            column="40"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.incomeText.text = String.format(&quot;Income: ₺%.2f&quot;, totalIncome)"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/HomeFragment.kt"
            line="75"
            column="39"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.incomeText.text = String.format(&quot;Income: ₺%.2f&quot;, totalIncome)"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/HomeFragment.kt"
            line="75"
            column="39"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.expenseText.text = String.format(&quot;Expense: ₺%.2f&quot;, totalExpense)"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/HomeFragment.kt"
            line="76"
            column="40"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.expenseText.text = String.format(&quot;Expense: ₺%.2f&quot;, totalExpense)"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/HomeFragment.kt"
            line="76"
            column="40"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                investmentAmount.text = String.format(&quot;₺%.2f&quot;, investment.amount)"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/InvestmentAdapter.kt"
            line="64"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                investmentAmount.text = String.format(&quot;₺%.2f&quot;, investment.amount)"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/InvestmentAdapter.kt"
            line="64"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.totalInvestmentsText.text = String.format(&quot;Total Investments: ₺%.2f&quot;, totalAmount)"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/InvestmentsFragment.kt"
            line="309"
            column="49"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            binding.totalInvestmentsText.text = String.format(&quot;Total Investments: ₺%.2f&quot;, totalAmount)"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/InvestmentsFragment.kt"
            line="309"
            column="49"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                amountText.text = String.format(&quot;₺%.2f&quot;, transaction.amount)"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/TransactionAdapter.kt"
            line="67"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                amountText.text = String.format(&quot;₺%.2f&quot;, transaction.amount)"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/TransactionAdapter.kt"
            line="67"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                amount.text = String.format(&quot;₺%.2f&quot;, student.amount)"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/WTStudentAdapter.kt"
            line="57"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                amount.text = String.format(&quot;₺%.2f&quot;, student.amount)"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/WTStudentAdapter.kt"
            line="57"
            column="31"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `font` is only used in API level 26 and higher (current min is 24)"
        errorLine1="        android:font=&quot;@font/opensans_regular&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/font/opensans.xml"
            line="5"
            column="9"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `fontStyle` is only used in API level 26 and higher (current min is 24)"
        errorLine1="        android:fontStyle=&quot;normal&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/font/opensans.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `fontWeight` is only used in API level 26 and higher (current min is 24)"
        errorLine1="        android:fontWeight=&quot;400&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/font/opensans.xml"
            line="7"
            column="9"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `font` is only used in API level 26 and higher (current min is 24)"
        errorLine1="        android:font=&quot;@font/opensans_bold&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/font/opensans.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `fontStyle` is only used in API level 26 and higher (current min is 24)"
        errorLine1="        android:fontStyle=&quot;normal&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/font/opensans.xml"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `fontWeight` is only used in API level 26 and higher (current min is 24)"
        errorLine1="        android:fontWeight=&quot;700&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/font/opensans.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="SelectedPhotoAccess"
        message="Your app is currently not handling Selected Photos Access introduced in Android 14+"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_MEDIA_IMAGES&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="6"
            column="36"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.15.0"
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.12.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="100"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0"
        errorLine1="    implementation &apos;androidx.appcompat:appcompat:1.6.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="101"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-ktx than 1.8.2 is available: 1.10.0"
        errorLine1="    implementation &apos;androidx.activity:activity-ktx:1.8.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="102"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.fragment:fragment-ktx than 1.6.2 is available: 1.8.6"
        errorLine1="    implementation &apos;androidx.fragment:fragment-ktx:1.6.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="103"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0"
        errorLine1="    implementation &apos;com.google.android.material:material:1.11.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="104"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.0"
        errorLine1="    implementation &apos;androidx.constraintlayout:constraintlayout:2.1.4&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="105"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.viewpager2:viewpager2 than 1.0.0 is available: 1.1.0"
        errorLine1="    implementation &quot;androidx.viewpager2:viewpager2:1.0.0&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="111"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-fragment-ktx than 2.7.7 is available: 2.8.7"
        errorLine1="    implementation &quot;androidx.navigation:navigation-fragment-ktx:$nav_version&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="114"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-ui-ktx than 2.7.7 is available: 2.8.7"
        errorLine1="    implementation &quot;androidx.navigation:navigation-ui-ktx:$nav_version&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="115"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="118"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.8.7"
        errorLine1="    implementation &quot;androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="119"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="122"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.5.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="123"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.work:work-runtime-ktx than 2.8.1 is available: 2.10.0"
        errorLine1="    implementation &quot;androidx.work:work-runtime-ktx:2.8.1&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="130"
            column="20"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="        android:tint=&quot;@color/white&quot;/>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_note_image.xml"
            line="22"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="This folder configuration (`v24`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `drawable`.">
        <location
            file="src/main/res/drawable-v24"/>
    </issue>

    <issue
        id="KaptUsageInsteadOfKsp"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp"
        errorLine1="    kapt &quot;androidx.room:room-compiler:$room_version&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="109"
            column="5"/>
    </issue>

    <issue
        id="KaptUsageInsteadOfKsp"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp"
        errorLine1="    kapt &apos;com.github.bumptech.glide:compiler:4.16.0&apos;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="127"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/Theme.AllinOne`)"
        errorLine1="    android:background=&quot;@color/white&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/colorPrimary` with a theme that also paints a background (inferred theme is `@style/Theme.AllinOne`)"
        errorLine1="    android:background=&quot;@color/colorPrimary&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/nav_header.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.mipmap.allinone_icon` appears to be unused">
        <location
            file="src/main/res/mipmap-hdpi/allinone_icon.jpg"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.button_background_color_selector` appears to be unused"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/color/button_background_color_selector.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.button_text_color_selector` appears to be unused"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/color/button_text_color_selector.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.dialog_add_investment` appears to be unused"
        errorLine1="&lt;androidx.core.widget.NestedScrollView"
        errorLine2="^">
        <location
            file="src/main/res/layout/dialog_add_investment.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.dialog_edit_transaction` appears to be unused"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/dialog_edit_transaction.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.mipmap.ic_launcher_round` appears to be unused">
        <location
            file="src/main/res/mipmap-hdpi/ic_launcher_round.webp"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_wallet` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_wallet.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.navigation.mobile_navigation` appears to be unused"
        errorLine1="&lt;navigation xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/navigation/mobile_navigation.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.amount` appears to be unused"
        errorLine1="    &lt;string name=&quot;amount&quot;>Amount&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.type` appears to be unused"
        errorLine1="    &lt;string name=&quot;type&quot;>Type&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="5"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.description` appears to be unused"
        errorLine1="    &lt;string name=&quot;description&quot;>Description&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.save_changes` appears to be unused"
        errorLine1="    &lt;string name=&quot;save_changes&quot;>Save Changes&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.type_input_description` appears to be unused"
        errorLine1="    &lt;string name=&quot;type_input_description&quot;>Select transaction type&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.select_type` appears to be unused"
        errorLine1="    &lt;string name=&quot;select_type&quot;>Select transaction type&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.investment_name` appears to be unused"
        errorLine1="    &lt;string name=&quot;investment_name&quot;>Investment Name&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.investment_type` appears to be unused"
        errorLine1="    &lt;string name=&quot;investment_type&quot;>Investment Type&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.add_image` appears to be unused"
        errorLine1="    &lt;string name=&quot;add_image&quot;>Add Image&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.delete_image` appears to be unused"
        errorLine1="    &lt;string name=&quot;delete_image&quot;>Delete Image&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.cancel` appears to be unused"
        errorLine1="    &lt;string name=&quot;cancel&quot;>Cancel&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.save` appears to be unused"
        errorLine1="    &lt;string name=&quot;save&quot;>Save&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive icon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="IconDipSize"
        message="The image `allinone_icon.jpg` varies significantly in its density-independent (dip) size across the various density versions: mipmap-hdpi/allinone_icon.jpg: 427x427 dp (640x640 px), mipmap-mdpi/allinone_icon.jpg: 640x640 dp (640x640 px), mipmap-xhdpi/allinone_icon.jpg: 320x320 dp (640x640 px), mipmap-xxhdpi/allinone_icon.jpg: 213x213 dp (640x640 px), mipmap-xxxhdpi/allinone_icon.jpg: 160x160 dp (640x640 px)">
        <location
            file="src/main/res/mipmap-xxxhdpi/allinone_icon.jpg"/>
        <location
            file="src/main/res/mipmap-xxhdpi/allinone_icon.jpg"/>
        <location
            file="src/main/res/mipmap-xhdpi/allinone_icon.jpg"/>
        <location
            file="src/main/res/mipmap-hdpi/allinone_icon.jpg"/>
        <location
            file="src/main/res/mipmap-mdpi/allinone_icon.jpg"/>
    </issue>

    <issue
        id="IconDuplicatesConfig"
        message="The `allinone_icon.jpg` icon has identical contents in the following configuration folders: mipmap-hdpi, mipmap-mdpi, mipmap-xhdpi, mipmap-xxhdpi, mipmap-xxxhdpi">
        <location
            file="src/main/res/mipmap-xxxhdpi/allinone_icon.jpg"/>
        <location
            file="src/main/res/mipmap-xxhdpi/allinone_icon.jpg"/>
        <location
            file="src/main/res/mipmap-xhdpi/allinone_icon.jpg"/>
        <location
            file="src/main/res/mipmap-mdpi/allinone_icon.jpg"/>
        <location
            file="src/main/res/mipmap-hdpi/allinone_icon.jpg"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_investment.xml"
            line="59"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="100"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_investment_image.xml"
            line="12"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageButton"
        errorLine2="     ~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_investment_image.xml"
            line="18"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_note_image.xml"
            line="8"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageButton"
        errorLine2="     ~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_note_image.xml"
            line="14"
            column="6"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="            &lt;AutoCompleteTextView"
        errorLine2="             ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_investment.xml"
            line="37"
            column="14"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;AutoCompleteTextView"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_investment.xml"
            line="32"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="                    &lt;AutoCompleteTextView"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="123"
            column="22"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead."
        errorLine1="        dialogBinding?.amountInput?.setText(investment?.amount?.toString())"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/InvestmentsFragment.kt"
            line="220"
            column="45"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            binding.investmentCountText.text = &quot;Number of Investments: ${investments.size}&quot;"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/InvestmentsFragment.kt"
            line="310"
            column="48"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            binding.investmentCountText.text = &quot;Number of Investments: ${investments.size}&quot;"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/InvestmentsFragment.kt"
            line="310"
            column="49"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                        binding.attachmentNameText.text = &quot;No attachment&quot;"
        errorLine2="                                                           ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.kt"
            line="81"
            column="60"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Number formatting does not take into account locale settings. Consider using `String.format` instead."
        errorLine1="            amountInput.setText(student.amount.toString())"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.kt"
            line="199"
            column="33"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    attachmentNameText.text = &quot;Attachment unavailable&quot;"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.kt"
            line="213"
            column="48"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        dialogBinding.attachmentNameText.text = &quot;Attachment: $fileName&quot;"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.kt"
            line="304"
            column="49"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        dialogBinding.attachmentNameText.text = &quot;Attachment: $fileName&quot;"
        errorLine2="                                                 ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.kt"
            line="304"
            column="50"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            dialogBinding.attachmentNameText.text = &quot;No attachment&quot;"
        errorLine2="                                                     ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/ui/wt/WTRegisterFragment.kt"
            line="336"
            column="54"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                dateRange.text = &quot;${dateFormat.format(student.startDate)} - ${dateFormat.format(student.endDate)}&quot;"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/allinone/adapters/WTStudentAdapter.kt"
            line="56"
            column="34"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Home&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Home&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/bottom_nav_menu.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Investments&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Investments&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/bottom_nav_menu.xml"
            line="10"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Name&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Name&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_investment.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Type&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Type&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_investment.xml"
            line="29"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Amount&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_investment.xml"
            line="46"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Description&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_investment.xml"
            line="61"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Images&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Add Images&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_investment.xml"
            line="90"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Title&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Title&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Bold&quot;, should use `@string` resource"
        errorLine1="                    android:contentDescription=&quot;Bold&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="43"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Italic&quot;, should use `@string` resource"
        errorLine1="                    android:contentDescription=&quot;Italic&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="51"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Underline&quot;, should use `@string` resource"
        errorLine1="                    android:contentDescription=&quot;Underline&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="59"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Bullet List&quot;, should use `@string` resource"
        errorLine1="                    android:contentDescription=&quot;Bullet List&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="73"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Numbered List&quot;, should use `@string` resource"
        errorLine1="                    android:contentDescription=&quot;Numbered List&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="81"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Image&quot;, should use `@string` resource"
        errorLine1="                    android:contentDescription=&quot;Add Image&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="95"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Note content&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Note content&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="108"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Attached Images&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Attached Images&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="120"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Image&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Add Image&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_note.xml"
            line="138"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Student Name&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Student Name&quot;>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Start Date&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Start Date&quot;>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="26"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;End Date&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;End Date&quot;>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="41"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Amount&quot;>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="56"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Payment Received&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Payment Received&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="70"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Attachment&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Attachment&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="77"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No attachment&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;No attachment&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="90"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Attachment&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Add Attachment&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="97"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Share Student Info&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Share Student Info&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="113"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Cancel&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Cancel&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="131"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Save&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Save&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_wt_student.xml"
            line="139"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Transactions&quot;, should use `@string` resource"
        errorLine1="            android:title=&quot;Transactions&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/drawer_menu.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;WT Registers&quot;, should use `@string` resource"
        errorLine1="            android:title=&quot;WT Registers&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/drawer_menu.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Notes&quot;, should use `@string` resource"
        errorLine1="            android:title=&quot;Notes&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/drawer_menu.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Current Balance&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;Current Balance&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="31"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Category Summary&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;Category Summary&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="77"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource"
        errorLine1="                    android:hint=&quot;Amount&quot;>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="106"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Category&quot;, should use `@string` resource"
        errorLine1="                    android:hint=&quot;Category&quot;>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="121"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Description (Optional)&quot;, should use `@string` resource"
        errorLine1="                    android:hint=&quot;Description (Optional)&quot;>"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="136"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Expense&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Expense&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="159"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Income&quot;, should use `@string` resource"
        errorLine1="                        android:text=&quot;Income&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_home.xml"
            line="170"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Investment Summary&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Investment Summary&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_investments.xml"
            line="29"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Total Investments: ₺0.00&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Total Investments: ₺0.00&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_investments.xml"
            line="38"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Number of Investments: 0&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Number of Investments: 0&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_investments.xml"
            line="46"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No investments yet. Add your first one!&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;No investments yet. Add your first one!&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_investments.xml"
            line="64"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Investment&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;Add Investment&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_investments.xml"
            line="77"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No notes yet. Add your first one!&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;No notes yet. Add your first one!&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_notes.xml"
            line="20"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Note&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;Add Note&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_notes.xml"
            line="33"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No events for this date&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;No events for this date&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_wt_calendar.xml"
            line="29"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add new student&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;Add new student&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_wt_register.xml"
            line="20"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Student&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;Add Student&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_wt_students.xml"
            line="21"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Note Image&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;Note Image&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_note.xml"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;AllInOne&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;AllInOne&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/nav_header.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Register&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Register&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/wt_bottom_nav_menu.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Calendar&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Calendar&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/wt_bottom_nav_menu.xml"
            line="10"
            column="9"/>
    </issue>

</issues>
