plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'kotlin-parcelize'
    id 'dagger.hilt.android.plugin'
    id 'com.diffplug.spotless'
    id 'androidx.navigation.safeargs.kotlin'
}

// Spotless configuration
spotless {
    kotlin {
        target '**/*.kt'
        ktlint('0.50.0')
        trimTrailingWhitespace()
        endWithNewline()
    }

    enforceCheck false
}

// Task to generate source code JSON
task generateSourceCodeJson(type: Exec) {
    workingDir project.rootDir
    commandLine 'python3', 'scripts/generate_source_code.py'
}

// Make sure the source code JSON is generated before building
preBuild.dependsOn generateSourceCodeJson

def getEnvValue(String key, String defaultVal = "NOT_SET") {
    def envFile = new File(rootDir, '.env')
    if (envFile.exists()) {
        def props = new Properties()
        envFile.withInputStream { stream ->
            props.load(stream)
        }
        return props.getProperty(key, defaultVal)
    } else {
        return System.getenv(key) ?: defaultVal
    }
}

android {
    namespace 'com.example.allinone'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.allinone"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // Read Instagram access token from .env file
        def instagramAccessToken = getEnvValue('INSTAGRAM_ACCESS_TOKEN')
        buildConfigField "String", "INSTAGRAM_ACCESS_TOKEN", "\"${instagramAccessToken}\""
        println "Instagram access token will be baked into the app: ${instagramAccessToken.take(4)}..."

        // Read Facebook Graph API token for Instagram insights
        def instagramGraphToken = getEnvValue('FACEBOOK_GRAPH_TOKEN')
        buildConfigField "String", "INSTAGRAM_GRAPH_TOKEN", "\"${instagramGraphToken}\""
        println "Instagram Graph API token will be baked into the app: ${instagramGraphToken.take(4)}..."

        // Read Instagram Business Account ID from .env file
        def instagramBusinessAccountId = getEnvValue('INSTAGRAM_BUSINESS_ACCOUNT_ID')
        buildConfigField "String", "INSTAGRAM_BUSINESS_ACCOUNT_ID", "\"${instagramBusinessAccountId}\""
        println "Instagram Business Account ID will be baked into the app: ${instagramBusinessAccountId.take(4)}..."

        // Read client secret from .env file
        def instagramClientSecret = getEnvValue('INSTAGRAM_CLIENT_SECRET')
        buildConfigField "String", "INSTAGRAM_CLIENT_SECRET", "\"${instagramClientSecret}\""
        println "Instagram client secret will be baked into the app: ${instagramClientSecret.take(4) != "NOT_" ? "Found" : "Not found"}"

        // Read Binance API keys from .env file
        def binanceApiKey = getEnvValue('BINANCE_API_KEY')
        buildConfigField "String", "BINANCE_API_KEY", "\"${binanceApiKey}\""
        println "Binance API key will be baked into the app: ${binanceApiKey.take(4) != "NOT_" ? "Found" : "Not found"}"

        def binanceSecretKey = getEnvValue('BINANCE_SECRET_KEY')
        buildConfigField "String", "BINANCE_SECRET_KEY", "\"${binanceSecretKey}\""
        println "Binance Secret key will be baked into the app: ${binanceSecretKey.take(4) != "NOT_" ? "Found" : "Not found"}"
    }

    signingConfigs {
        debug {
            storeFile file("../my-release-key.jks")
            storePassword "goktugoner2323"
            keyAlias System.getenv("KEY_ALIAS") ?: "my-alias"
            keyPassword System.getenv("KEY_PASSWORD") ?: "goktugoner2323"
        }
        release {
            storeFile file("../my-release-key.jks")
            storePassword System.getenv("KEYSTORE_PASSWORD") ?: "goktugoner2323"
            keyAlias System.getenv("KEY_ALIAS") ?: "my-alias"
            keyPassword System.getenv("KEY_PASSWORD") ?: "goktugoner2323"
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            debuggable false
        }
        debug {
            signingConfig signingConfigs.debug
            debuggable true
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
    }

    tasks.withType(JavaCompile) {
        options.compilerArgs << '-Xlint:unchecked'
    }

    tasks.register('cleanBuildDir', Delete) {
        delete "${buildDir}"
    }

    lint {
        baseline = file("lint-baseline.xml")
        abortOnError = false
    }

    kapt {
        correctErrorTypes true

        // Only include essential kapt arguments
        arguments {
            // Only keep the kapt specific argument
            arg("kapt.kotlin.generated", "${buildDir}/generated/source/kapt/")
        }
    }

    // Ensure Hilt compiler options are properly handled
    hilt {
        enableAggregatingTask = true
    }
}

dependencies {
    def nav_version = "2.7.7"
    def lifecycle_version = "2.7.0"
    def firebase_version = "32.8.0"
    def hilt_version = "2.48.1" // Updated to latest stable version

    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.activity:activity-ktx:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // Core Splash Screen API
    implementation 'androidx.core:core-splashscreen:1.0.1'

    // Google Play Services - Must come before Firebase
    implementation 'com.google.android.gms:play-services-basement:18.3.0'
    implementation 'com.google.android.gms:play-services-base:18.3.0'
    implementation 'com.google.android.gms:play-services-auth:21.0.0'

    // Firebase
    implementation platform("com.google.firebase:firebase-bom:$firebase_version")
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-firestore'
    implementation 'com.google.firebase:firebase-storage'
    implementation 'com.google.firebase:firebase-crashlytics'

    // Gson for JSON serialization/deserialization
    implementation 'com.google.code.gson:gson:2.10.1'

    implementation "androidx.viewpager2:viewpager2:1.0.0"

    // Navigation component
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"

    // Lifecycle
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"

    // Hilt for dependency injection
    implementation "com.google.dagger:hilt-android:$hilt_version"
    kapt "com.google.dagger:hilt-android-compiler:$hilt_version"
    // Add Hilt testing dependencies
    testImplementation "com.google.dagger:hilt-android-testing:$hilt_version"
    kaptTest "com.google.dagger:hilt-android-compiler:$hilt_version"

    // Security for encryption
    implementation "androidx.security:security-crypto:1.1.0-alpha06"

    // Paging for large datasets
    implementation "androidx.paging:paging-runtime-ktx:3.2.1"

    // Timber for better logging
    implementation 'com.jakewharton.timber:timber:5.0.1'

    // DocumentFile support for file operations
    implementation 'androidx.documentfile:documentfile:1.0.1'

    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.4.0'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.1.0'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'

    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation "com.google.dagger:hilt-android-testing:$hilt_version"
    kaptAndroidTest "com.google.dagger:hilt-android-compiler:$hilt_version"

    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'

    // WorkManager for background tasks
    implementation "androidx.work:work-runtime-ktx:2.8.1"

    // MPAndroidChart for charts
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

    // KnifeText for rich text editing
    implementation 'com.github.mthli:Knife:v1.1'
}